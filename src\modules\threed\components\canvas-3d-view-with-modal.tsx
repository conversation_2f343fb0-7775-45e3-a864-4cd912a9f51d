"use client";
import { ModalDrillhole } from "@/modules/drillhole/components/modal-drillhole";
import React, { useEffect, useState } from "react";
import { getAllDesurveyResultByDrillHole } from "../redux/threedSlice/thunks";
import MemoizedCanvas3DView from "./canvas-3d-view";
import GeologyLogModal from "./geology-log-modal";

// Import custom CSS for 3D view components
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import "../styles/3d-view.css";

// Render the drill hole modal outside the Canvas
// This is necessary because the modal needs to be in the DOM outside the Canvas context
const Canvas3DViewWithModal: React.FC<{
  modelUrl?: string | null;
  modelCoordinates?: { east: number; north: number; rl: number };
  useFileCoordinates?: boolean;
}> = (props) => {
  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    detailInfo: any;
    type: string;
  }>({
    isOpen: false,
    detailInfo: null,
    type: "update",
  });

  // State for geology log modal
  const [geologyLogModal, setGeologyLogModal] = useState<{
    visible: boolean;
    geologyData: any;
    drillholeName: string;
  }>({
    visible: false,
    geologyData: null,
    drillholeName: "",
  });

  const dispatch = useAppDispatch();
  // Get global project ID and prospect ID from Redux store if available
  const globalProjectId = useAppSelector(
    (state: any) => state.user?.userInfo?.projectId
  );
  const globalProspectId = useAppSelector(
    (state: any) => state.user?.userInfo?.prospectId
  );

  // Function to refresh drill hole data after edit
  const fetchListDrillhole = () => {
    dispatch(
      getAllDesurveyResultByDrillHole({
        projectId: globalProjectId,
        skipCount: 0,
        maxResultCount: 1000,
      })
    );
  };

  // Add event listener for the editDrillhole custom event from DrillholeModel
  useEffect(() => {
    const handleEditDrillhole = (event: CustomEvent) => {
      // Use getDetailDrillhole to get complete drill hole data
      setModalState({
        isOpen: true,
        type: "update",
        detailInfo: event.detail,
      });
    };

    const handleShowGeologyLog = (event: CustomEvent) => {
      setGeologyLogModal({
        visible: true,
        geologyData: event.detail.geologyData,
        drillholeName: event.detail.drillholeName,
      });
    };

    // Add event listeners
    document.addEventListener(
      "editDrillhole",
      handleEditDrillhole as EventListener
    );
    document.addEventListener(
      "showGeologyLog",
      handleShowGeologyLog as EventListener
    );

    // Clean up
    return () => {
      document.removeEventListener(
        "editDrillhole",
        handleEditDrillhole as EventListener
      );
      document.removeEventListener(
        "showGeologyLog",
        handleShowGeologyLog as EventListener
      );
    };
  }, [dispatch]);

  return (
    <>
      <div className="threejs-canvas-container w-full h-full">
        <MemoizedCanvas3DView {...props} />
      </div>
      {modalState.isOpen && (
        <ModalDrillhole
          fetchListDrillhole={fetchListDrillhole}
          modalState={modalState}
          setModalState={setModalState}
        />
      )}

      {/* Geology Log Modal */}
      <GeologyLogModal
        visible={geologyLogModal.visible}
        onClose={() =>
          setGeologyLogModal({ ...geologyLogModal, visible: false })
        }
        geologyData={geologyLogModal.geologyData}
        drillholeName={geologyLogModal.drillholeName}
      />
    </>
  );
};

export default Canvas3DViewWithModal;

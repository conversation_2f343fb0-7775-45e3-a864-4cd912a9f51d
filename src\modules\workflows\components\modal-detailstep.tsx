import { useGetListSourceTypeWorkflows } from "@/modules/workflows/hooks/useGetListSourceTypeWorkFlow.hook";
import { ButtonCommon } from "@/components/common/button-common";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { SelectCommon } from "@/components/common/select-common";
import { TextAreaCommon } from "@/components/common/textarea-common";
import { InputCheckCommon } from "@/components/common/input-check";
import { Switch, TreeSelect } from "antd";
import {
  DATA_TYPE,
  MODEL_TYPE,
  OUTPUT_TYPE,
  PROCESS_TYPE,
  TOOL_TYPE,
} from "@/constants/general.const";
import { Form } from "antd";
import { useParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import {
  StepTypeWorkflowsBodyType,
  StepWorkflowsBody,
} from "../model/schema/stepworkflow.schema";
import { useGetListStepWorkflow } from "../hooks/useGetListStepWorkflow.hook";
import { useCreateStepWorkflow } from "../hooks/useCreateStepWorkFlow.hook";
import { useUpdateStepworkFlow } from "../hooks/useUpdateStepworkflow.hook";
import { useDeleteStepworkflow } from "../hooks/useDeleteStepWorkflow.hook";
import { zodResolver } from "@hookform/resolvers/zod";
import { ToogleCommon } from "@/components/common/toogle-common";
import { useQueryImageType } from "@/modules/image-type/hooks/useQueryImageType";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { transformToHierarchicalNodes } from "@/modules/image/helpers/image.helpers";

export interface IModelDetailStepProps {
  modalState: {
    isOpen: boolean;
    detailInfo: any;
    type: string;
  };
  setModalState: (value: any) => void;
  fetchListStepWorkflow: () => void;
  listPolygons: any[];
}

export function ModalDetailStep(props: IModelDetailStepProps) {
  const { modalState, setModalState, fetchListStepWorkflow, listPolygons } =
    props;
  const params = useParams();
  const id = Number(params.id[0]);
  const { request: requestGetListSourceType, data: dataListSourceType } =
    useGetListSourceTypeWorkflows();
  const { request: requestStepWorkflow, data: dataStepWorkflow } =
    useGetListStepWorkflow();
  const [requestCreateStepWorkflow] = useCreateStepWorkflow();
  const [requestUpdateStepworkflow] = useUpdateStepworkFlow();
  const [requestDeleteStepworkflow] = useDeleteStepworkflow();
  const [loading, setLoading] = useState(false);
  const [processTypeSelected, setProcessTypeSelected] = useState(0);
  const [boundingBoxOptions, setBoundingBoxOptions] = useState<any[]>([]);
  const [boundingRowsOptions, setBoundingRowsOptions] = useState<any[]>([]);

  useEffect(() => {
    requestGetListSourceType({
      maxResultCount: 10,
      skipCount: 0,
      keyword: "",
    });
  }, []);

  useEffect(() => {
    requestStepWorkflow({
      maxResultCount: 10,
      skipCount: 0,
      keyword: "",
      WorkFlowId: id as any,
    });
  }, []);

  useEffect(() => {
    if (listPolygons) {
      setBoundingBoxOptions(
        listPolygons
          ?.filter((polygon: any) => polygon?.type === 1)
          .map((item: any) => {
            return {
              value: item?.id,
              label: item?.name,
            };
          })
      );
      setBoundingRowsOptions(
        listPolygons
          ?.filter((polygon: any) => polygon?.type === 2)
          .map((item: any) => {
            return {
              value: item?.id,
              label: item?.name,
            };
          })
      );
    }
  }, []);

  const { control, handleSubmit, setValue, getValues, watch, reset } =
    useForm<StepTypeWorkflowsBodyType>({
      resolver: zodResolver(StepWorkflowsBody),
      defaultValues: {
        name: modalState?.detailInfo?.name,
        processType: modalState?.detailInfo?.processType,
        modelId: modalState?.detailInfo?.modelId,
        toolType: modalState?.detailInfo?.toolType,
        polygonId: modalState?.detailInfo?.polygonId,
        dataSourceType: modalState?.detailInfo?.dataSourceType,
        dataValue: modalState?.detailInfo?.dataValue,
        outputType: modalState?.detailInfo?.outputType,
        boundingBoxId: modalState?.detailInfo?.boundingBoxId,
        boundingRowsId: modalState?.detailInfo?.boundingRowsId,
        prompt: modalState?.detailInfo?.prompt,
        segmentFlag: modalState?.detailInfo?.segmentFlag,
        isCropAdditional: modalState?.detailInfo?.isCropAdditional ?? false,
        imageTypesAdditional: modalState?.detailInfo?.imageTypesAdditional,
        imageSubtypesAdditional:
          modalState?.detailInfo?.imageSubtypesAdditional,
      },
    });

  // Reset form when modalState.detailInfo changes
  useEffect(() => {
    if (modalState?.detailInfo && modalState.type === "update") {
      reset({
        name: modalState.detailInfo.name,
        processType: modalState.detailInfo.processType,
        modelId: modalState.detailInfo.modelId,
        toolType: modalState.detailInfo.toolType,
        polygonId: modalState.detailInfo.polygonId,
        dataSourceType: modalState.detailInfo.dataSourceType,
        dataValue: modalState.detailInfo.dataValue,
        outputType: modalState.detailInfo.outputType,
        boundingBoxId: modalState.detailInfo.boundingBoxId,
        boundingRowsId: modalState.detailInfo.boundingRowsId,
        prompt: modalState.detailInfo.prompt,
        segmentFlag: modalState.detailInfo.segmentFlag,
        isCropAdditional: modalState.detailInfo.isCropAdditional ?? false,
        imageTypesAdditional: modalState.detailInfo.imageTypesAdditional,
        imageSubtypesAdditional: modalState.detailInfo.imageSubtypesAdditional,
      });
    } else if (modalState.type === "create") {
      // Reset form to empty state when creating new
      reset({
        name: "",
        processType: undefined,
        modelId: undefined,
        toolType: undefined,
        polygonId: undefined,
        dataSourceType: undefined,
        dataValue: undefined,
        outputType: undefined,
        boundingBoxId: undefined,
        boundingRowsId: undefined,
        prompt: "",
        segmentFlag: false,
        isCropAdditional: false,
        imageTypesAdditional: undefined,
        imageSubtypesAdditional: undefined,
      });
    }
    // Reset user change flag when form is reset
    setIsUserChange(false);
  }, [modalState?.detailInfo, modalState.type, reset]);

  const isConfirm = modalState.type === "delete";

  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };
  const onSubmit = (values: StepTypeWorkflowsBodyType) => {
    const payload = {
      ...values,
      workflowId: id as any,
    };

    if (modalState.type === "create") {
      requestCreateStepWorkflow(
        payload,
        setLoading,
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Create a step successfully");
          fetchListStepWorkflow();
        },
        () => {}
      );
    }
    if (modalState.type === "update") {
      requestUpdateStepworkflow(
        {
          ...payload,
          id: modalState.detailInfo.id,
        },
        setLoading,
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Update a step successfully");
          fetchListStepWorkflow();
        },
        () => {}
      );
    }
  };

  const handleDelete = () => {
    requestDeleteStepworkflow(
      {
        id: modalState.detailInfo.id,
      },
      setLoading,
      () => {
        setModalState({ ...modalState, isOpen: false });
        toast.success("Delete a step successfully");

        fetchListStepWorkflow();
      },
      () => {}
    );
  };

  const checkDataType = (value: string) => {
    switch (value) {
      case "original":
      case "cropped":
      case "croppedRow":
      case "segmented":
      case "mask":
        return DATA_TYPE.Image.value;

      default:
        return DATA_TYPE.Step.value;
    }
  };

  const getOutputType = (value: StepTypeWorkflowsBodyType) => {
    if (value.modelId === 1) {
      return OUTPUT_TYPE.Text;
    }

    return OUTPUT_TYPE.Image;
  };

  // Watch all form values
  const watchedValues = watch();

  useEffect(() => {
    if (watchedValues.processType && watchedValues.processType !== 0) {
      setProcessTypeSelected(watchedValues.processType);
    }
    if (watchedValues.processType === PROCESS_TYPE.AiBase.value) {
      setValue("toolType", undefined);
      setValue("polygonId", undefined);
    } else if (watchedValues.processType === PROCESS_TYPE.Tool.value) {
      setValue("modelId", undefined);
    }
    if (watchedValues.toolType === TOOL_TYPE.RotateClockwise.value) {
      setValue("polygonId", undefined);
    }
    if (watchedValues.toolType === TOOL_TYPE.RotateCounterClockwise.value) {
      setValue("polygonId", undefined);
    }
    if (watchedValues.toolType === TOOL_TYPE.Align.value) {
      setValue("polygonId", undefined);
    }

    // Set dataSourceType based on dataValue whenever it changes
    if (watchedValues.dataValue) {
      setValue("dataSourceType", checkDataType(watchedValues.dataValue));
    }

    // Set outputType based on modelId whenever it changes
    setValue("outputType", getOutputType(watchedValues));
  }, [
    watchedValues.processType,
    watchedValues.toolType,
    watchedValues.dataValue,
    watchedValues.modelId,
  ]);
  const toolType = watch("toolType");
  const modelId = watch("modelId");
  const {
    data: imageTypeData,
    isLoading: isFetchingImageType,
    setSearchParams: setSearchParamsImageType,
  } = useQueryImageType(); // Assuming isLoading or isFetching is returned
  const allImageTypes = imageTypeData?.data?.items;
  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );
  useEffect(() => {
    if (globalProjectId) {
      setSearchParamsImageType((prev) => ({
        ...prev,
        projectId: globalProjectId,
      }));
    }
  }, [globalProjectId, setSearchParamsImageType]);
  const transformDataToTreeNodes = (data: any[] | undefined) => {
    if (!data) return [];
    return data.map((type: any) => ({
      title: type.name,
      value: `type-${type.id}`,
      key: `type-${type.id}`,
      children: (type.imageSubtypes || []).map((subtype: any) => ({
        title: subtype.name,
        value: `subtype-${subtype.id}-type-${type.id}`,
        key: `subtype-${subtype.id}-type-${type.id}`,
      })),
    }));
  };
  const imageTypeTreeData = useMemo(
    () => transformDataToTreeNodes(allImageTypes),
    [imageTypeData]
  );
  const [selectedNodeValues, setSelectedNodeValues] = useState<string[]>([]);
  const [selectedHierarchicalNodes, setSelectedHierarchicalNodes] =
    useState<any>();
  const treeSelectValue = useMemo(() => {
    return selectedNodeValues || [];
  }, [selectedNodeValues]);

  const handleImageTypeTreeSelectChange = (selectedValues: string[]) => {
    setIsUserChange(true);
    setSelectedNodeValues(selectedValues);
    const hierarchicalNodes = transformToHierarchicalNodes(
      selectedValues,
      imageTypeTreeData
    );
    setSelectedHierarchicalNodes(hierarchicalNodes);
  };

  // Reset selectedNodeValues when form is reset
  useEffect(() => {
    if (modalState.type === "create") {
      setSelectedNodeValues([]);
    } else if (modalState?.detailInfo && modalState.type === "update") {
      // Set selectedNodeValues based on imageTypesAdditional and imageSubtypesAdditional
      const selectedValues: string[] = [];

      // Only add subtypes, not types (to avoid selecting all subtypes in a type)
      if (modalState.detailInfo.imageSubtypesAdditional) {
        modalState.detailInfo.imageSubtypesAdditional.forEach(
          (subtypeId: number) => {
            // Find the parent type for this subtype
            const imageType = allImageTypes?.find((type) =>
              type.imageSubtypes?.some(
                (subtype: any) => subtype.id === subtypeId
              )
            );
            if (imageType) {
              selectedValues.push(`subtype-${subtypeId}-type-${imageType.id}`);
            }
          }
        );
      }

      // Only add types that don't have any subtypes selected
      if (modalState.detailInfo.imageTypesAdditional) {
        modalState.detailInfo.imageTypesAdditional.forEach((typeId: number) => {
          // Check if this type has any subtypes selected
          const hasSubtypesSelected =
            modalState.detailInfo.imageSubtypesAdditional?.some(
              (subtypeId: number) => {
                const imageType = allImageTypes?.find((type) =>
                  type.imageSubtypes?.some(
                    (subtype: any) => subtype.id === subtypeId
                  )
                );
                return imageType?.id === typeId;
              }
            );

          // Only add type if no subtypes are selected for this type
          if (!hasSubtypesSelected) {
            selectedValues.push(`type-${typeId}`);
          }
        });
      }

      setSelectedNodeValues(selectedValues);
    }
  }, [modalState?.detailInfo, modalState.type, allImageTypes]);

  // Sync form values with selectedNodeValues (only when user changes TreeSelect)
  const [isUserChange, setIsUserChange] = useState(false);
  useEffect(() => {
    // Only update form values when user changes TreeSelect, not when form is reset
    if (isUserChange) {
      // Parse all selected values to extract type and subtype IDs
      const selectedTypeIds = new Set<number>();
      const selectedSubTypeIds = new Set<number>();

      selectedNodeValues.forEach((value) => {
        if (value.includes("subtype")) {
          // Format: "subtype-{subtypeId}-type-{typeId}"
          const parts = value.split("-");
          const subTypeId = Number(parts[1]);
          const typeId = Number(parts[3]);

          // Add both the parent type and the specific subtype
          selectedTypeIds.add(typeId);
          selectedSubTypeIds.add(subTypeId);
        } else if (value.includes("type") && !value.includes("subtype")) {
          // Format: "type-{typeId}"
          const typeId = Number(value.split("-")[1]);

          // Add the type and all its subtypes (when user explicitly selects a type)
          selectedTypeIds.add(typeId);
          const imageType = allImageTypes?.find((type) => type.id === typeId);
          if (imageType?.imageSubtypes) {
            imageType.imageSubtypes.forEach((subtype) => {
              selectedSubTypeIds.add(subtype.id);
            });
          }
        }
      });

      // Update form values
      setValue("imageTypesAdditional", Array.from(selectedTypeIds));
      setValue("imageSubtypesAdditional", Array.from(selectedSubTypeIds));
      setIsUserChange(false); // Reset flag after updating
    }
  }, [selectedNodeValues, allImageTypes, setValue, isUserChange]);

  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      {isConfirm ? (
        <div className="flex flex-col gap-2">
          <p className="font-bold text-16-18 capitalize font-visby">
            Are you sure you want to delete this sequence?
          </p>
          <p>
            This action cannot be undone. This will permanently delete the
            sequence
          </p>
          <div className="flex justify-end gap-2">
            <ButtonCommon
              onClick={() => {
                setModalState({
                  ...modalState,

                  isOpen: false,
                });
              }}
              className="btn btn-sm"
            >
              No
            </ButtonCommon>
            <ButtonCommon
              loading={loading}
              onClick={handleDelete}
              className="btn btn-sm bg-primary text-white hover:bg-primary"
            >
              Yes
            </ButtonCommon>
          </div>
        </div>
      ) : (
        <div className="flex flex-col gap-4">
          <p className="font-bold text-24-28 capitalize text-center font-visby">
            {modalState.type === "update" ? "Update a step" : "Create a step"}
          </p>
          <Form
            onFinish={handleSubmit(onSubmit, (errors) => {
              console.log(errors);
            })}
            className="flex flex-col gap-3"
          >
            {/* Basic Information */}
            <div className="mb-2">
              <InputTextCommon
                label="Name"
                name="name"
                placeholder="Type step name here"
                control={control}
              />
            </div>

            {/* Process Type Selection */}
            <div className="mb-2">
              <SelectCommon
                label="Process Type"
                name="processType"
                control={control}
                placeholder="Select Process Type"
                options={Object.values(PROCESS_TYPE)}
              />
            </div>

            {/* Data Source Selection - Moved up for better flow */}
            <div className="mb-2">
              <SelectCommon
                label="Data Source Value"
                name="dataValue"
                control={control}
                placeholder="Select Data Source Value"
                options={[
                  {
                    label: <span>Image Type</span>,
                    title: "Image Type",
                    options: [
                      { value: "original", label: "Original" },
                      // { value: "cropped", label: "Cropped Box" },
                      // { value: "croppedRow", label: "Cropped Row" },
                      // { value: "segmented", label: "Segmented" },
                      // { value: "mask", label: "Mask" },
                    ],
                  },
                ]}
              />
            </div>

            {/* AI Model Options */}
            {processTypeSelected === PROCESS_TYPE.AiBase.value && (
              <div className="mb-2 border-l-2 border-primary pl-3 py-1">
                <SelectCommon
                  label="Model"
                  name="modelId"
                  control={control}
                  placeholder="Select Model"
                  options={[
                    {
                      value: 8,
                      label: "Autocrop and core outlines",
                    },
                    {
                      value: 11,
                      label: "Autocrop",
                    },

                    {
                      value: 13,
                      label: "Autocrop and Pieces RCNN",
                    },
                    {
                      value: 14,
                      label: "Segment Auto Crop and Core Outlines",
                    },
                    {
                      value: 15,
                      label: "Process Core Outline",
                    },
                  ]}
                />

                {/* AI Model specific settings */}
                {(modelId === MODEL_TYPE.AutocropAndCoreOutline.value ||
                  modelId === MODEL_TYPE.AutocropAndPiecesRCNN.value) && (
                  <>
                    <div className="mt-3">
                      <div className="flex flex-row items-center justify-between mb-3">
                        <span className="text-sm font-medium">Use row</span>
                        <Switch
                          checked={watch("segmentFlag") ?? false}
                          onChange={(checked) =>
                            setValue("segmentFlag", checked)
                          }
                        />
                      </div>

                      <TextAreaCommon
                        label="Text Extraction Prompt"
                        name="prompt"
                        control={control}
                        autoSize
                        placeholder="i.e: Extract a depth from this image that is between DFm and DTm. If you don't find the text, do an interpretation of the data to predict the most likely depth value. Return result as single number."
                        description={
                          <>
                            <p className="font-bold">Variables:</p>
                            <ul className="list-disc list-inside">
                              <li>
                                <span>DFm = Image depth from.</span>
                              </li>
                              <li>
                                <span>DTm = Image depth to.</span>
                              </li>
                            </ul>
                          </>
                        }
                      />
                    </div>
                  </>
                )}
              </div>
            )}

            {/* Tool Options */}
            {processTypeSelected === PROCESS_TYPE.Tool.value && (
              <div className="mb-2 border-l-2 border-primary pl-3 py-1">
                <SelectCommon
                  label="Tool Type"
                  name="toolType"
                  control={control}
                  placeholder="Select Tool Type"
                  options={Object.values(TOOL_TYPE)}
                />

                {/* Tool specific settings */}
                {toolType === TOOL_TYPE.AssignRow.value && (
                  <div className="mt-3">
                    <SelectCommon
                      label="Bounding Rows"
                      name="boundingRowsId"
                      control={control}
                      placeholder="Select Bounding Rows"
                      options={boundingRowsOptions}
                    />
                  </div>
                )}

                {toolType === TOOL_TYPE.AssignBox.value && (
                  <div className="mt-3">
                    <SelectCommon
                      label="Bounding Box"
                      name="boundingBoxId"
                      control={control}
                      placeholder="Select Bounding Box"
                      options={boundingBoxOptions}
                    />
                  </div>
                )}
              </div>
            )}

            <ToogleCommon
              label="Crop Additional"
              control={control}
              name="isCropAdditional"
            />
            {watch("isCropAdditional") && (
              <div className="flex flex-col gap-2">
                <div className="font-medium">Image Types & Subtypes</div>
                <TreeSelect
                  size="large"
                  treeData={imageTypeTreeData}
                  value={treeSelectValue}
                  onChange={handleImageTypeTreeSelectChange}
                  multiple={true}
                  treeCheckable={true}
                  allowClear={true}
                  showSearch={false}
                  placeholder="Select image types and/or subtypes"
                  style={{ width: "100%" }}
                  showCheckedStrategy={TreeSelect.SHOW_PARENT}
                  loading={isFetchingImageType}
                  className="w-full h-full"
                  treeDefaultExpandAll // Optional: to show all nodes expanded by default
                />
              </div>
            )}

            <div className="flex flex-col gap-3 mt-3">
              <ButtonCommon
                loading={loading}
                type="submit"
                className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
              >
                {modalState.type === "update" ? "Update Step" : "Create Step"}
              </ButtonCommon>
              <ButtonCommon
                onClick={handleCancel}
                className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
              >
                Cancel
              </ButtonCommon>
            </div>
          </Form>
        </div>
      )}
    </ModalCommon>
  );
}

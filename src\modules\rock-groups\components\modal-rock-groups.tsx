import { ModalType } from "@/common/configs/app.enum";
import { Button<PERSON>ommon } from "@/components/common/button-common";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { ToogleCommon } from "@/components/common/toogle-common";
import { useGetListRockType } from "@/modules/rock-type/hooks/useGetListRockType";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button, Form, Select } from "antd";
import { useEffect, useState, useCallback } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useAssignRockType } from "../hooks/useAssignRockType";
import { useCreateRockGroup } from "../hooks/useCreateRockGroups";
import { useDeleteRockGroup } from "../hooks/useDeleteRockGroups";
import { useGetRockGroup } from "../hooks/useGetRockGroups";
import { useUpdateRockGroup } from "../hooks/useUpdateRockGroups";
import {
  RockGroup,
  RockGroupBodyType,
} from "../model/schema/rock-groups.schema";

export interface IModalCompanyProps {
  modalState: {
    isOpen: boolean;
    detailInfo: any;
    type: string;
  };
  setModalState: (value: any) => void;
  refresh: () => void;
}

export function ModalRockGroup(props: IModalCompanyProps) {
  const { modalState, setModalState, refresh } = props;
  const { request: requestCreateRockGroup, loading: loadingCreateRockGroup } =
    useCreateRockGroup();
  const { request: requestUpdateRockGroup, loading: loadingUpdateRockGroup } =
    useUpdateRockGroup();
  const { request: requestDeleteRockGroup, loading: loadingDeleteRockGroup } =
    useDeleteRockGroup();
  const { control, handleSubmit, setValue, getValues } =
    useForm<RockGroupBodyType>({
      resolver: zodResolver(RockGroup),
      defaultValues: {
        ...modalState?.detailInfo,
        isActive: modalState?.detailInfo?.isActive ?? true,
      },
    });
  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };
  const isConfirm = modalState.type === ModalType.DELETE;
  const isAssign = modalState.type === ModalType.ASSIGN;
  const onSubmit = (values: RockGroupBodyType) => {
    if (modalState.type === ModalType.CREATE) {
      requestCreateRockGroup(
        values,
        (res) => {
          toast.success("Create rock style successfully");
          requestAssignRockType(
            {
              rockGroupId: res?.id,
              rockTypeIds: selectedRockType,
            },
            () => {
              setModalState({ ...modalState, isOpen: false });
              refresh();
            }
          );
          refresh();
        },
        (err) => {
          toast.error(err);
        }
      );
    }
    if (modalState.type === ModalType.UPDATE) {
      requestUpdateRockGroup(
        {
          ...values,
          id: modalState.detailInfo.id,
        },
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Update rock group successfully");
          refresh();
          handleAsssign();
        },
        (err) => {
          toast.error(err);
        }
      );
    }
  };

  const handleDelete = () => {
    requestDeleteRockGroup(
      {
        id: modalState.detailInfo.id,
      },
      () => {
        setModalState({ ...modalState, isOpen: false });
        refresh();
      },
      (err) => {
        toast.error(err);
      }
    );
  };
  const [selectedRockType, setSelectedRockType] = useState<any[]>([]);

  const { data: rockTypes, request: requestRockTypes } = useGetListRockType();

  const { request: requestAssignRockType, loading: loadingAssignRockType } =
    useAssignRockType();
  const handleAsssign = () => {
    requestAssignRockType(
      {
        rockGroupId: modalState.detailInfo.id,
        rockTypeIds: selectedRockType,
      },
      () => {
        setModalState({ ...modalState, isOpen: false });
        refresh();
      }
    );
  };
  const { request: requestRockGroup } = useGetRockGroup();
  useEffect(() => {
    if (!modalState.detailInfo?.id) return;
    requestRockGroup(modalState.detailInfo?.id, (data) => {
      const rockTypes = data?.rockTypes?.map((item) => item.id);
      setSelectedRockType(rockTypes);
    });
  }, [modalState?.detailInfo?.id]);

  // Debounced function to request rock types
  let searchRockTypeTimeoutId: NodeJS.Timeout;
  const debouncedRequestRockTypes = useCallback(
    (keyword: string) => {
      clearTimeout(searchRockTypeTimeoutId);
      searchRockTypeTimeoutId = setTimeout(() => {
        requestRockTypes({
          name: keyword,
          maxResultCount: 1000,
        });
      }, 300); // 300ms debounce delay
    },
    [requestRockTypes]
  );

  useEffect(() => {
    requestRockTypes({
      maxResultCount: 1000,
    });
  }, []);

  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      {isConfirm ? (
        <div className="flex flex-col gap-2">
          <p className="font-bold text-16-18 capitalize font-visby">
            Are you sure you want to delete this rock group?
          </p>
          <p>
            This action cannot be undone. This will permanently delete the rock
            group
          </p>
          <div className="flex justify-end gap-2">
            <ButtonCommon onClick={handleCancel} className="btn btn-sm">
              No
            </ButtonCommon>
            <ButtonCommon
              loading={loadingDeleteRockGroup}
              onClick={handleDelete}
              className="btn btn-sm bg-primary text-white hover:bg-primary"
            >
              Yes
            </ButtonCommon>
          </div>
        </div>
      ) : isAssign ? (
        <div className="px-6 flex flex-col gap-2">
          <p className="font-bold text-24-28 capitalize text-center font-visby">
            Assign Rock Group
          </p>
          <p className="font-medium">Rock Type</p>
          <Select
            options={rockTypes?.map((item) => ({
              label: item.name,
              value: item.id,
            }))}
            mode="multiple"
            placeholder="Select Rock Type"
            allowClear
            value={selectedRockType}
            onChange={(value) => {
              setSelectedRockType(value);
            }}
          />
          <Button
            type="primary"
            onClick={handleAsssign}
            loading={loadingAssignRockType}
          >
            Assign
          </Button>
        </div>
      ) : (
        <div className="px-6 flex flex-col gap-4">
          <p className="font-bold text-24-28 capitalize text-center font-visby">
            {modalState.type === ModalType.UPDATE
              ? "Update rock group"
              : "Add rock group"}
          </p>
          <Form
            onFinish={handleSubmit(onSubmit)}
            className="flex flex-col gap-3"
          >
            <InputTextCommon
              label="Name"
              name="name"
              placeholder="Type rock group name here"
              control={control}
              isRequired={true}
            />

            <p className="font-medium">Rock Type </p>

            <Select
              options={rockTypes?.map((item) => ({
                label: item.name,
                value: item.id,
              }))}
              mode="multiple"
              placeholder="Select Rock Type"
              allowClear
              value={selectedRockType}
              onSearch={(value) => {
                debouncedRequestRockTypes(value);
              }}
              onChange={(value) => {
                setSelectedRockType(value);
              }}
              showSearch
              onBlur={() => debouncedRequestRockTypes("")}
              filterOption={false}
            />
            <ToogleCommon label="Is Active" name="isActive" control={control} />

            <div className="flex flex-col gap-3 mt-3">
              <ButtonCommon
                type="submit"
                loading={loadingCreateRockGroup || loadingUpdateRockGroup}
                className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
              >
                {modalState.type === ModalType.UPDATE
                  ? "Update rock group"
                  : "Add rock group"}
              </ButtonCommon>
              <ButtonCommon
                onClick={handleCancel}
                className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
              >
                Cancel
              </ButtonCommon>
            </div>
          </Form>
        </div>
      )}
    </ModalCommon>
  );
}

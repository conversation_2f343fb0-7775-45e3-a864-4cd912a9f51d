import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/common/modal-common";
import { useGetListRockType } from "@/modules/rock-type/hooks/useGetListRockType";
import { Select } from "antd";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import rockGroupsRequest from "../api/rock-groups.api";
import { useGetRockGroup } from "../hooks/useGetRockGroups";

export default function ModalAddRockType({
  isOpen,
  id,
  setIsOpen,
  refresh,
}: {
  isOpen: boolean;
  id: string;
  setIsOpen: (isOpen: boolean) => void;
  refresh: () => void;
}) {
  const { request: requestGetRockGroup, loading: loadingGetRockGroup } =
    useGetRockGroup();

  const [keywordRockType, setKeywordRockType] = useState("");
  const [selectedRockType, setSelectedRockType] = useState<any[]>([]);
  const [assignRockType, setAssignRockType] = useState<any[]>([]);

  const [pageRockType, setPageRockType] = useState(1);

  useEffect(() => {
    requestRockTypes({
      name: keywordRockType,
      maxResultCount: 1000,
      skipCount: (pageRockType - 1) * 50,
      isActive: true,
    });
  }, [keywordRockType, pageRockType]);
  const {
    data: rockTypes,
    request: requestRockTypes,
    loading: loadingRockTypes,
    total: totalRockTypes,
  } = useGetListRockType();
  useEffect(() => {
    if (id) {
      requestGetRockGroup(id, (res) => {
        const rockTypeIds = res?.rockTypes?.map((item) => item.id);
        setAssignRockType(rockTypeIds);
      });
    }
  }, [id]);
  const [loading, setLoading] = useState(false);
  return (
    <ModalCommon
      title="Add Rock Type"
      width={450}
      open={isOpen}
      onCancel={() => setIsOpen(false)}
      okText="Add"
      centered
      confirmLoading={loading}
      onOk={() => {
        setLoading(true);
        const payload = [...assignRockType, selectedRockType];
        rockGroupsRequest
          .assignRockType({
            rockGroupId: id as any,
            rockTypeIds: payload,
          })
          .then(() => {
            toast.success("Add rock type success");
            setIsOpen(false);
            refresh();
          })
          .catch((e) => {
            toast.error(e.message);
          })
          .finally(() => {
            setLoading(false);
          });
      }}
    >
      <div className="flex flex-col gap-3">
        <Select
          options={rockTypes
            ?.map((item) => ({
              label: item.name,
              value: item.id,
            }))
            .filter((item) => !assignRockType.includes(item.value))}
          placeholder="Select Rock Type"
          allowClear
          value={selectedRockType}
          onChange={(value) => {
            setSelectedRockType(value);
            setKeywordRockType("");
          }}
          showSearch
          filterOption={false}
          searchValue={keywordRockType}
          onSearch={(value) => {
            setKeywordRockType(value);
          }}
          onBlur={() => {
            setKeywordRockType("");
          }}
        />
      </div>
    </ModalCommon>
  );
}

import { RequestState } from "@/common/configs/app.contants";
import { createAppAsyncThunk } from "@/common/vendors/redux/store/createAppAsyncThunk";
import downholeDataRequest from "@/modules/downhole-point/api/down-hole-data.api";
import threedRequest from "../../api/threed.api";

export interface GetDrillholesParams {
  keyword?: string;
  projectIds?: number[];
  prospectIds?: number[];
  skipCount?: number;
  maxResultCount?: number;
  sortField?: string;
  sortOrder?: string;
  drillHoleStatus?: 1 | 2 | 3 | 4 | 5;
  isActive?: boolean;
  skipCoordinateRecalculation?: boolean; // Flag to skip recalculating coordinates (for refresh)
}
export interface GetAllDesurveyResultByDrillHoleParams {
  projectId: number;
  skipCount?: number;
  maxResultCount?: number;
}

export const getDrillholes = createAppAsyncThunk(
  "threed/getDrillholes",
  async (params?: GetDrillholesParams) => {
    const response = await threedRequest.getDrillholes(params);
    return response;
  }
);

export interface IDesurveyResult {
  drillHoleId: number;
  depth: number;
  northing: number;
  easting: number;
  elevation: number;
  id: number;
}
export interface IThreedDrillholeData {
  id: string;
  name: string;
  projectCode: string;
  east: number | null;
  north: number | null;
  rl: number | null;
  depth: number | null;
  dip: number;
  azimuth: number | null;
  creationTime: string;
  croppedRows: number;
  desurveyResults: IDesurveyResult[];
  drillHoleStatus: number;
  easting: number | null;
  elevation: number | null;
  isActive: boolean;
  isExist: boolean;
  isExport: boolean;
  latitude: number | null;
  longitude: number | null;
  maxDepth: number;
  northing: number | null;
  originalImages: number;
  project: any;
  prospect: any;
}

// :
// "2025-06-24T07:02:52.095776"
// croppedRows
// :
// 0
// desurveyResults
// :
// []

// drillHoleStatus
// :
// 2
// easting
// :
// null
// elevation
// :
// null
// id
// :
// 508
// isActive
// :
// true
// isExist
// :
// false
// isExport
// :
// false
// latitude
// :
// null
// longitude
// :
// null
// maxDepth
// :
// 0
// name
// :
// "24CAP"
// northing
// :
// null
// originalImages
// :
// 0
// project
// :
// null
// prospect
// :
// null

// New thunk that uses getAllDesurveyResultByDrillHole and transforms data to DrillholeData format
export const getAllDesurveyResultByDrillHole = createAppAsyncThunk(
  "threed/getDrillholesFromDesurvey",
  async (params: GetAllDesurveyResultByDrillHoleParams) => {
    const response = await downholeDataRequest.getAllDesurveyResultByDrillHole(
      params
    );

    if (response.state === RequestState.success && response.data) {
      // Transform desurvey data to DrillholeData format
      const drillholeData = (response.data?.items || []).map(
        (item: IThreedDrillholeData) => ({
          id: item.id?.toString() || "",
          name: item.name || "",
          projectCode: item.project?.name || "",
          east: item.easting || item.east || 0,
          north: item.northing || item.north || 0,
          rl: item.elevation || item.rl || 0,
          depth: item.maxDepth || item.depth || 0,
          dip: item.dip || 90, // Default to vertical if not provided
          azimuth: item.azimuth || 0,
          desurveyResults: item.desurveyResults || [],
          drillHoleStatus: item.drillHoleStatus || 0,
          easting: item.easting || null,
          elevation: item.elevation || null,
          isActive: item.isActive || false,
          isExist: item.isExist || false,
          isExport: item.isExport || false,
          latitude: item.latitude || null,
        })
      );
      // .filter(
      //   (item) =>
      //     // Filter out drillholes with completely invalid coordinates
      //     !(item.east === 0 && item.north === 0 && item.rl === 0)
      // );

      console.log("Transformed drillhole data:", drillholeData);

      // Log statistics about desurvey data
      const drillholesWithDesurvey = drillholeData;
      // const drillholesWithDesurvey = drillholeData.filter(
      //   (d) => d.desurveyResults && d.desurveyResults.length > 0
      // );
      console.log(
        `Drillholes with desurvey data: ${drillholesWithDesurvey.length}/${drillholeData.length}`
      );

      if (drillholesWithDesurvey.length > 0) {
        const sampleDesurvey = drillholesWithDesurvey[0];
        console.log(
          "Sample desurvey data for drillhole",
          sampleDesurvey.id,
          ":",
          sampleDesurvey.desurveyResults
        );
      }

      // Check for negative coordinates
      const drillholesWithNegativeCoords = drillholeData.filter(
        (d) => d.east < 0 || d.north < 0
      );
      if (drillholesWithNegativeCoords.length > 0) {
        console.log(
          "Drillholes with negative coordinates:",
          drillholesWithNegativeCoords.length
        );
        console.log(
          "Sample negative coordinates:",
          drillholesWithNegativeCoords[0]
        );
      }

      return {
        state: RequestState.success,
        data: drillholeData,
      };
    }

    return response;
  }
);

export interface GetGeologyDataParams {
  geologySuiteId: number;
  drillholeIds: string; // JSON stringified array
  geologySuiteFieldIds?: string; // JSON stringified array
  sortOrder: string;
}

export const getGeologyData = createAppAsyncThunk(
  "threed/getGeologyData",
  async (params: GetGeologyDataParams) => {
    const response = await threedRequest.getGeologyData(params);
    return response;
  }
);

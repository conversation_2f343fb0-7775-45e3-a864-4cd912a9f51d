import { ReduxState } from "@/common/vendors/redux/store/store";

export interface DrillholeStyle {
  traceColor: string;
  traceWidth: number;
  rockGroupWidth: number;
}

export interface RockTypeTextStyle {
  fontSize: number;
  color: string;
  fontWeight: string;
  visible: boolean;
}

export interface LayerSettings {
  visible: boolean;
  transparency: number; // 0-1 where 0 is opaque and 1 is fully transparent
  lighting: {
    ambient: {
      color: string;
      intensity: number;
    };
    directional: {
      color: string;
      intensity: number;
      position: [number, number, number];
    };
  };
}

export const selectDrillholes = (state: ReduxState) => state.threeD.drillholes;
export const selectGeologyData = (drillholeId: string) => (state: ReduxState) =>
  state.threeD.geologyData[drillholeId];
export const selectSelectedDrillholes = (state: ReduxState) =>
  state.threeD.selectedDrillholes;
export const selectVisibilityMap = (state: ReduxState) =>
  state.threeD.visibilityMap;
export const selectCamera = (state: ReduxState) => state.threeD.camera;
export const selectSectionConfig = (state: ReduxState) =>
  state.threeD.sectionConfig;
export const selectShowLabels = (state: ReduxState) => state.threeD.showLabels;
export const selectShowLegend = (state: ReduxState) => state.threeD.showLegend;
export const selectCoordinateBase = (state: ReduxState) =>
  state.threeD.coordinateBase;
export const selectTheme = (state: ReduxState) => state.threeD.theme;
export const selectShowGrid = (state: ReduxState) => state.threeD.showGrid;
export const selectDrillholeStyle = (state: ReduxState): DrillholeStyle =>
  state.threeD.drillholeStyle;

export const selectRockTypeTextStyle = (state: ReduxState): RockTypeTextStyle =>
  state.threeD.rockTypeTextStyle;

// New selectors for rotation center functionality
export const selectSelectingRotationCenter = (state: ReduxState) =>
  state.threeD.selectingRotationCenter;

export const selectShowRotationCenterMarker = (state: ReduxState) =>
  state.threeD.showRotationCenterMarker;

export const selectShowSelectionMessage = (state: ReduxState) =>
  state.threeD.showSelectionMessage;

// Geology suite and rock group selectors
export const selectGeologySuiteId = (state: ReduxState) =>
  state.threeD.geologySuiteId;

export const selectRockGroupId = (state: ReduxState) =>
  state.threeD.rockGroupId;

export const selectGeologySuites = (state: ReduxState) =>
  state.threeD.geologySuites;

export const selectRockGroups = (state: ReduxState) => state.threeD.rockGroups;

export const selectDrillholeDisplayPanelLoaded = (state: ReduxState) =>
  state.threeD.drillholeDisplayPanelLoaded;

// Assay suite and attribute selectors
export const selectAssaySuiteId = (state: ReduxState) =>
  state.threeD.assaySuiteId;

export const selectAssayAttributeId = (state: ReduxState) =>
  state.threeD.assayAttributeId;

export const selectAssaySuites = (state: ReduxState) =>
  state.threeD.assaySuites;

export const selectAssayAttributes = (state: ReduxState) =>
  state.threeD.assayAttributes;

export const selectAssayPanelLoaded = (state: ReduxState) =>
  state.threeD.assayPanelLoaded;

export const selectAssayData = (state: ReduxState) => state.threeD.assayData;

// Drillhole display mode selector
export const selectDrillholeDisplayMode = (state: ReduxState) =>
  state.threeD.drillholeDisplayMode;

// Dual layer system selectors
export const selectLayers = (state: ReduxState) => state.threeD.layers;

export const selectLayer =
  (layerName: "objectLayer" | "drillholeLayer") =>
  (state: ReduxState): LayerSettings =>
    state.threeD.layers[layerName];

export const selectLayerVisibility =
  (layerName: "objectLayer" | "drillholeLayer") =>
  (state: ReduxState): boolean =>
    state.threeD.layers[layerName].visible;

export const selectLayerTransparency =
  (layerName: "objectLayer" | "drillholeLayer") =>
  (state: ReduxState): number =>
    state.threeD.layers[layerName].transparency;

export const selectLayerLighting =
  (layerName: "objectLayer" | "drillholeLayer") => (state: ReduxState) =>
    state.threeD.layers[layerName].lighting;

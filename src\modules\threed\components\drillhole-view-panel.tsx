"use client";
import { RequestState } from "@/common/configs/app.contants";
import {
  BarChartOutlined,
  ExperimentOutlined,
  FileOutlined,
  ReloadOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import { Button, message, Space, Tag, Tooltip } from "antd";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getAllDesurveyResultByDrillHole } from "../redux/threedSlice/thunks";
import { Logging3DView } from "./3d-view";
import DisplayModeToggle from "./display-mode-toggle";

// This component will be the main container for the 3D view and related controls
const DrillholeViewPanel: React.FC = () => {
  const dispatch = useDispatch();
  // Toggle control panels visibility
  const [showControls, setShowControls] = useState(true);
  const [showDrillholeList, setShowDrillholeList] = useState(true);
  const [showGeologySuite, setShowGeologySuite] = useState(true);
  const [showAssayPanel, setShowAssayPanel] = useState(true);
  // State to store the uploaded model URL and file name
  const [modelUrl, setModelUrl] = useState<string | null>(null);
  const [fileName, setFileName] = useState<string | null>(null);

  // Get global project ID and prospect ID from Redux store if available
  const globalProjectId = useSelector(
    (state: any) => state.user?.userInfo?.projectId
  );
  const globalProspectId = useSelector(
    (state: any) => state.user?.userInfo?.prospectId
  );

  // Get drillholes loading state from Redux
  const isDrillholesLoading =
    useSelector((state: any) => state.threeD?.drillholes?.status) ===
    RequestState.pending;

  // Handle file upload
  const handleFileUpload = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const files = target.files;
    if (files && files.length > 0) {
      const file = files[0];
      if (file.name.endsWith(".gltf") || file.name.endsWith(".glb")) {
        // Create URL from the selected file
        const url = URL.createObjectURL(file);
        setModelUrl(url);
        setFileName(file.name);
        message.success(`File "${file.name}" uploaded successfully`);
      } else {
        message.error("Only .gltf and .glb files are supported");
      }
    }
  };

  // Clean up object URL when component unmounts or when modelUrl changes
  useEffect(() => {
    return () => {
      if (modelUrl) {
        URL.revokeObjectURL(modelUrl);
      }
    };
  }, [modelUrl]);

  // Function to remove the uploaded 3D model
  const handleRemoveModel = () => {
    if (modelUrl) {
      // Revoke the object URL to free up memory
      URL.revokeObjectURL(modelUrl);
      setModelUrl(null);
      message.success(`File "${fileName}" removed successfully`);
      setFileName(null);
    }
  };

  // Handler for refreshing drill hole data
  const handleRefreshData = () => {
    // Only proceed if we have valid project and prospect IDs
    if (!globalProjectId || !globalProspectId) {
      message.warning("Project or prospect not selected");
      return;
    }

    // Set loading state
    dispatch(
      getAllDesurveyResultByDrillHole({
        projectId: globalProjectId,
        skipCount: 0,
        maxResultCount: 1000,
      }) as any
    ).unwrap();
  };

  return (
    <div className="h-full">
      {/* Header with title, control buttons, and loading indicator */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-bold">Drillhole 3D Visualization</h2>
          <Button
            type="primary"
            onClick={() => setShowControls(!showControls)}
            className="ml-4"
            style={{ width: 110 }}
          >
            {showControls ? "Hide Controls" : "Show Controls"}
          </Button>
          <Button
            type="primary"
            style={{ width: 120 }}
            onClick={() => setShowDrillholeList(!showDrillholeList)}
          >
            {showDrillholeList ? "Hide Drillholes" : "Show Drillholes"}
          </Button>
          <Button
            type="primary"
            style={{ width: 120 }}
            onClick={() => {
              setShowGeologySuite(!showGeologySuite);
              setShowAssayPanel(!showAssayPanel);
            }}
            icon={
              <>
                <ExperimentOutlined />
                <BarChartOutlined />
              </>
            }
          >
            {showGeologySuite && showAssayPanel ? "Hide Suites" : "Show Suites"}
          </Button>
          <DisplayModeToggle />
          <Tooltip title="Refresh Data (Keep Scene View)">
            <Button
              icon={<ReloadOutlined />}
              type="primary"
              onClick={handleRefreshData}
              loading={isDrillholesLoading}
            >
              {isDrillholesLoading ? "Refreshing..." : "Refresh"}
            </Button>
          </Tooltip>
          <Space>
            <Button
              type="primary"
              icon={<UploadOutlined />}
              onClick={() => {
                // Create and trigger a hidden file input element
                const fileInput = document.createElement("input");
                fileInput.type = "file";
                fileInput.accept = ".gltf,.glb";
                fileInput.onchange = handleFileUpload;
                fileInput.click();
              }}
            >
              Upload 3D Model
            </Button>
            {fileName && (
              <Tag
                color="blue"
                icon={<FileOutlined />}
                closable
                onClose={handleRemoveModel}
                title={`Click × to remove ${fileName}`}
              >
                {fileName}
              </Tag>
            )}
          </Space>
        </div>
      </div>

      <Logging3DView
        showControls={showControls}
        setShowControls={setShowControls}
        showDrillholeList={showDrillholeList}
        setShowDrillholeList={setShowDrillholeList}
        showGeologySuite={showGeologySuite}
        setShowGeologySuite={setShowGeologySuite}
        showAssayPanel={showAssayPanel}
        setShowAssayPanel={setShowAssayPanel}
        modelUrl={modelUrl}
      />
    </div>
  );
};

export default DrillholeViewPanel;

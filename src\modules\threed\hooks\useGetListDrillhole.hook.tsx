import { RequestState } from "@/common/configs/app.contants";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { DrillholeData } from "../interface/threed.interface";
import { selectDrillholes } from "../redux/threedSlice/selectors";
import { getAllDesurveyResultByDrillHole } from "../redux/threedSlice/thunks";

export const useGetListDrillhole = () => {
  const dispatch = useAppDispatch();

  // Get global project ID from Redux store
  const globalProjectId = useAppSelector(
    (state: any) => state.user?.userInfo?.projectId
  );

  // The initial state when the slice is not yet registered
  const defaultState = {
    data: [],
    status: RequestState.idle,
  };

  // Use a try-catch to handle the case where the threed slice is not registered
  let drillholesState;
  try {
    drillholesState = useSelector(selectDrillholes) || defaultState;
  } catch (error) {
    drillholesState = defaultState;
  }

  const { data, status, error } = drillholesState;
  const [drillholes, setDrillholes] = useState<DrillholeData[]>([]);

  useEffect(() => {
    if (status === RequestState.idle && globalProjectId) {
      // @ts-ignore - Bypassing type check temporarily until we resolve the Redux store typing
      dispatch(
        getAllDesurveyResultByDrillHole({
          projectId: globalProjectId,
          skipCount: 0,
          maxResultCount: 1000,
        })
      );
    }

    if (status === RequestState.success && data) {
      setDrillholes(data);
    }
  }, [dispatch, status, data, globalProjectId]);

  return {
    drillholes,
    isLoading: status === RequestState.pending,
    error,
  };
};
